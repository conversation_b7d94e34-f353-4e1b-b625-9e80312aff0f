"""
Enhanced verification code parser supporting multiple languages and formats.
"""
import html
import re
from dataclasses import dataclass
from typing import List, Optional, Pattern, Tuple


@dataclass
class VerificationPattern:
    """Represents a verification code pattern with its metadata."""
    name: str
    pattern: Pattern[str]
    priority: int  # Higher priority patterns are checked first
    description: str


class VerificationCodeParser:
    """
    Enhanced parser for extracting verification codes from email content.

    Supports multiple languages, formats, and extraction strategies.
    """

    def __init__(self):
        """Initialize the parser with comprehensive patterns and keywords."""
        self._setup_keywords()
        self._setup_patterns()

    def _setup_keywords(self) -> None:
        """Setup multilingual keywords for verification code detection."""
        self.verification_keywords = {
            # 中文
            "chinese": [
                "验证码", "验证代码", "确认码", "动态码", "安全码", "校验码",
                "认证码", "身份验证码", "短信验证码", "邮箱验证码"
            ],
            # English
            "english": [
                "verification code", "verification", "verify", "code", "passcode",
                "pin", "token", "otp", "one-time password", "authentication code",
                "auth code", "security code", "confirmation code", "access code",
                "login code", "2fa code", "two-factor", "sms code", "email code"
            ],
            # 日本語
            "japanese": [
                "認証コード", "確認コード", "認証番号", "パスコード", "ワンタイムパスワード",
                "セキュリティコード", "ログインコード", "二段階認証", "SMS認証"
            ],
            # 한국어
            "korean": [
                "인증번호", "인증코드", "확인번호", "보안코드", "일회용비밀번호",
                "로그인코드", "2단계인증", "SMS인증"
            ],
            # Español
            "spanish": [
                "código de verificación", "código", "verificación", "código de acceso",
                "código de seguridad", "código de confirmación", "pin", "clave"
            ],
            # Français
            "french": [
                "code de vérification", "code", "vérification", "code d'accès",
                "code de sécurité", "code de confirmation", "authentification"
            ],
            # Deutsch
            "german": [
                "bestätigungscode", "verifizierungscode", "sicherheitscode",
                "zugangscode", "authentifizierungscode", "pin"
            ],
            # Русский
            "russian": [
                "код подтверждения", "код верификации", "код", "пароль",
                "код безопасности", "код доступа", "пин-код"
            ],
            # Português
            "portuguese": [
                "código de verificação", "código", "verificação", "código de acesso",
                "código de segurança", "pin", "senha"
            ],
            # Italiano
            "italian": [
                "codice di verifica", "codice", "verifica", "codice di accesso",
                "codice di sicurezza", "pin", "password"
            ],
            # Nederlands
            "dutch": [
                "verificatiecode", "bevestigingscode", "toegangscode",
                "beveiligingscode", "pincode"
            ],
            # العربية
            "arabic": [
                "رمز التحقق", "كود التحقق", "رمز الأمان", "كود الأمان", "رمز المرور"
            ]
        }

        # Flatten all keywords for easy searching
        self.all_keywords = []
        for lang_keywords in self.verification_keywords.values():
            self.all_keywords.extend(lang_keywords)

    def _setup_patterns(self) -> None:
        """Setup verification code patterns with different priorities."""
        self.patterns = [
            # High priority: Exact format patterns
            VerificationPattern(
                name="standard_numeric",
                pattern=re.compile(r'\b\d{4,8}\b'),
                priority=100,
                description="Standard 4-8 digit numeric codes"
            ),
            VerificationPattern(
                name="alphanumeric_mixed",
                pattern=re.compile(r'\b[A-Z0-9]{4,8}\b'),
                priority=90,
                description="Mixed alphanumeric codes (uppercase)"
            ),
            VerificationPattern(
                name="alphanumeric_lower",
                pattern=re.compile(r'\b[a-z0-9]{4,8}\b'),
                priority=85,
                description="Mixed alphanumeric codes (lowercase)"
            ),
            VerificationPattern(
                name="hyphenated_code",
                pattern=re.compile(r'\b\d{2,4}-\d{2,4}(?:-\d{2,4})?\b'),
                priority=95,
                description="Hyphenated numeric codes (e.g., 12-34-56)"
            ),
            VerificationPattern(
                name="spaced_code",
                pattern=re.compile(r'\b\d{1,2}\s+\d{1,2}\s+\d{1,2}(?:\s+\d{1,2})?\b'),
                priority=80,
                description="Space-separated numeric codes"
            ),
            VerificationPattern(
                name="bracketed_code",
                pattern=re.compile(r'\[(\d{4,8})\]|\((\d{4,8})\)'),
                priority=85,
                description="Codes in brackets or parentheses"
            ),
            VerificationPattern(
                name="quoted_code",
                pattern=re.compile(r'["\'](\d{4,8})["\']'),
                priority=85,
                description="Codes in quotes"
            ),
            # Medium priority: Context-based patterns
            VerificationPattern(
                name="colon_separated",
                pattern=re.compile(r':\s*(\d{4,8})\b'),
                priority=70,
                description="Codes after colon"
            ),
            VerificationPattern(
                name="is_pattern",
                pattern=re.compile(r'\bis\s+(\d{4,8})\b', re.IGNORECASE),
                priority=75,
                description="Codes after 'is'"
            ),
            # Lower priority: Broader patterns
            VerificationPattern(
                name="long_numeric",
                pattern=re.compile(r'\b\d{9,12}\b'),
                priority=30,
                description="Longer numeric codes (might be phone numbers)"
            ),
            VerificationPattern(
                name="very_long_alphanumeric",
                pattern=re.compile(r'\b[A-Za-z0-9]{16,64}\b'),
                priority=20,
                description="Very long alphanumeric strings (tokens)"
            )
        ]

        # Sort patterns by priority (highest first)
        self.patterns.sort(key=lambda p: p.priority, reverse=True)

        # Setup link patterns
        self.link_patterns = [
            re.compile(r'https?://[^\s"\'<>]*(?:verify|activate|confirm|validate|auth)[^\s"\'<>]*', re.IGNORECASE),
            re.compile(r'https?://[^\s"\'<>]*[?&](?:code|token|verify)=[^\s"\'<>&]*', re.IGNORECASE),
            re.compile(r'https?://[^\s"\'<>]*(?:verification|confirmation|authentication)[^\s"\'<>]*', re.IGNORECASE)
        ]

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for better parsing."""
        if not text:
            return ""

        # Decode HTML entities
        text = html.unescape(text)

        # Remove HTML tags (basic cleanup)
        text = re.sub(r'<[^>]+>', ' ', text)

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove common email formatting artifacts
        text = re.sub(r'=\r?\n', '', text)  # Remove soft line breaks
        text = re.sub(r'[=\*_]{2,}', ' ', text)  # Remove formatting markers

        return text.strip()

    def _find_keyword_contexts(self, text: str) -> List[Tuple[str, int, int]]:
        """Find all keyword contexts in the text."""
        contexts = []
        text_lower = text.lower()

        for keyword in self.all_keywords:
            keyword_lower = keyword.lower()
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break

                # Extract context around the keyword
                context_start = max(0, pos - 50)
                context_end = min(len(text), pos + len(keyword) + 100)
                context = text[context_start:context_end]

                contexts.append((context, pos, pos + len(keyword)))
                start = pos + 1

        return contexts

    def _extract_codes_from_context(self, context: str) -> List[Tuple[str, int]]:
        """Extract potential verification codes from a context string."""
        codes = []

        for pattern in self.patterns:
            matches = pattern.pattern.finditer(context)
            for match in matches:
                # Get the actual code (handle grouped patterns)
                if match.groups():
                    # For patterns with groups, find the first non-None group
                    code = None
                    for group in match.groups():
                        if group is not None:
                            code = group
                            break
                    if code is None:
                        code = match.group(0)
                else:
                    code = match.group(0)

                # Special handling for hyphenated and spaced codes
                if pattern.name in ["hyphenated_code", "spaced_code"]:
                    # Remove hyphens and spaces to get clean code
                    clean_code = re.sub(r'[-\s]', '', code)
                    codes.append((clean_code, pattern.priority))
                else:
                    codes.append((code, pattern.priority))

        return codes

    def _extract_links(self, text: str) -> List[str]:
        """Extract verification links from text."""
        links = []

        for pattern in self.link_patterns:
            matches = pattern.finditer(text)
            for match in matches:
                links.append(match.group(0))

        return links

    def _score_code(self, code: str, context: str, priority: int) -> int:
        """Score a potential verification code based on various factors."""
        if not code:  # Handle None or empty code
            return -100

        score = priority

        # Length scoring
        if 4 <= len(code) <= 6:
            score += 20
        elif len(code) == 8:
            score += 15
        elif len(code) < 4 or len(code) > 12:
            score -= 30

        # Pattern scoring
        if code.isdigit():
            score += 10

        # Context scoring
        context_lower = context.lower()
        high_confidence_phrases = [
            "your code is", "verification code is", "code:", "验证码：", "验证码是",
            "your verification code", "authentication code", "security code"
        ]

        for phrase in high_confidence_phrases:
            if phrase in context_lower:
                score += 25
                break

        # Avoid common false positives
        false_positive_patterns = [
            r'\b\d{4}\s*年',  # Years in Chinese
            r'\b\d{1,2}:\d{2}',  # Time format
            r'\b\d{4}-\d{2}-\d{2}',  # Date format
            r'\b\d{10,}',  # Very long numbers (likely phone/ID)
            r'\b19\d{2}\b',  # Years like 1990
            r'\b20\d{2}\b',  # Years like 2024
        ]

        for pattern in false_positive_patterns:
            if re.search(pattern, context):
                score -= 50  # Stronger penalty for false positives

        return score

    def parse(self, text: str) -> Optional[str]:
        """
        Parse verification code or link from email text.

        Args:
            text: Email body text (plain text or HTML)

        Returns:
            Extracted verification code/link or None if not found
        """
        if not text:
            return None

        # Clean the text
        cleaned_text = self._clean_text(text)

        # First try to find verification links (highest priority)
        links = self._extract_links(cleaned_text)
        if links:
            return links[0]  # Return the first link found immediately

        # Find keyword contexts
        contexts = self._find_keyword_contexts(cleaned_text)

        # Extract and score codes from keyword contexts
        all_codes = []

        for context, _, _ in contexts:
            codes = self._extract_codes_from_context(context)
            for code, priority in codes:
                score = self._score_code(code, context, priority)
                all_codes.append((code, score, context))

        # If no keyword contexts found, search in the entire text but with much lower priority
        if not contexts:
            codes = self._extract_codes_from_context(cleaned_text)
            for code, priority in codes:
                # Much lower priority for codes found without keywords
                adjusted_priority = priority - 60
                score = self._score_code(code, cleaned_text, adjusted_priority)
                # Only consider codes that look like actual verification codes
                if len(code) >= 4 and code.isdigit():
                    all_codes.append((code, score, cleaned_text))

        # Return the highest scoring code if it has a reasonable score
        if all_codes:
            all_codes.sort(key=lambda x: x[1], reverse=True)
            best_code, best_score, _ = all_codes[0]

            # Only return codes with positive scores
            if best_score > 0:
                return best_code

        return None

    def parse_multiple(self, text: str, max_results: int = 5) -> List[Tuple[str, int]]:
        """
        Parse multiple potential verification codes with their scores.

        Args:
            text: Email body text
            max_results: Maximum number of results to return

        Returns:
            List of (code, score) tuples sorted by score
        """
        if not text:
            return []

        cleaned_text = self._clean_text(text)
        contexts = self._find_keyword_contexts(cleaned_text)

        if not contexts:
            contexts = [(cleaned_text, 0, len(cleaned_text))]

        all_codes = []
        seen_codes = set()

        for context, _, _ in contexts:
            codes = self._extract_codes_from_context(context)
            for code, priority in codes:
                if code not in seen_codes:
                    score = self._score_code(code, context, priority)
                    all_codes.append((code, score))
                    seen_codes.add(code)

        # Sort by score and return top results
        all_codes.sort(key=lambda x: x[1], reverse=True)
        return all_codes[:max_results]
