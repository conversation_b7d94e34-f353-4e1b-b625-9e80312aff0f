import functools
import time
from typing import Any, Callable, Dict, List, Optional, TypeVar

import requests

from .verification_parser import VerificationCodeParser

T = TypeVar("T")


def _retry_with_backoff(retries: int = 3, backoff_factor: float = 0.5) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    A decorator for retrying a function with exponential backoff.
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            last_exception = None
            for i in range(retries):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    last_exception = e
                    sleep_time = backoff_factor * (2 ** i)
                    print(f"Request failed: {e}. Retrying in {sleep_time:.2f} seconds...")
                    time.sleep(sleep_time)
            if last_exception:
                raise last_exception
            # This part should ideally not be reached if retries > 0
            # but as a fallback we raise a generic error.
            raise RuntimeError("Retry mechanism failed without a caught exception.")
        return wrapper
    return decorator


class MailVerifierClient:
    """A client for interacting with a mail API to fetch messages and verification codes."""

    def __init__(self, token: str, base_url: str = "https://api.mail.tm"):
        """
        Initializes the MailVerifierClient.

        Args:
            token (str): The authentication token for the API.
            base_url (str, optional): The base URL of the mail API. Defaults to "https://api.mail.tm".
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({"Authorization": f"Bearer {token}"})
        self.verification_parser = VerificationCodeParser()

    @_retry_with_backoff()
    def get_messages(self) -> List[Dict[str, Any]]:
        """
        Retrieves a list of all messages. Retries on network failure.

        Returns:
            List[Dict[str, Any]]: A list of messages.
        """
        response = self.session.get(f"{self.base_url}/messages")
        response.raise_for_status()
        data = response.json()
        return data.get("hydra:member", [])

    @_retry_with_backoff()
    def get_message_by_id(self, message_id: str) -> Dict[str, Any]:
        """
        Retrieves a single message by its ID. Retries on network failure.

        Args:
            message_id (str): The ID of the message to retrieve.

        Returns:
            Dict[str, Any]: The message data.
        """
        response = self.session.get(f"{self.base_url}/messages/{message_id}")
        response.raise_for_status()
        return response.json()

    def _parse_verification_info(self, body: str) -> Optional[str]:
        """
        Parses the verification code or link from the email body.
        Uses the enhanced VerificationCodeParser for better accuracy.
        """
        return self.verification_parser.parse(body)

    def get_latest_verification_code(
        self, timeout: int = 60, interval: int = 5
    ) -> Optional[str]:
        """
        Polls for new messages and extracts the latest verification code or link.

        Args:
            timeout (int, optional): Total time in seconds to wait for a message. Defaults to 60.
            interval (int, optional): Time in seconds between polling attempts. Defaults to 5.

        Returns:
            Optional[str]: The found verification code/link, or None if not found within the timeout.
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            messages = self.get_messages()
            if messages:
                latest_message = messages[0]
                message_id = latest_message.get("id")
                if message_id:
                    full_message = self.get_message_by_id(message_id)
                    # Extract text body, falling back to html if necessary
                    text_body = full_message.get("text")
                    if not text_body and full_message.get("html"):
                        # In a real scenario, we might want to strip HTML tags
                        text_body = str(full_message["html"])

                    if text_body:
                        code = self._parse_verification_info(text_body)
                        if code:
                            return code
            time.sleep(interval)
        return None
