import functools
import time
from typing import Any, Callable, Dict, List, Optional, TypeVar

import requests

from .verification_parser import VerificationCodeParser
from .exceptions import (
    MailVerifierError, NetworkError, TimeoutError, APIError,
    AuthenticationError, AuthorizationError, RateLimitError,
    ValidationError, MessageNotFoundError, VerificationCodeNotFoundError,
    RetryExhaustedError, classify_http_error, classify_network_error
)
from .logging import get_logger, log_api_request, log_api_response
from .validators import validate_and_clean_message, clean_email_content, get_validator
from .cache import get_message_cache, get_parse_cache, cached_api_call
from .polling import SmartPoller, PollingStrategy, create_smart_poller

T = TypeVar("T")


def _retry_with_backoff(retries: int = 3, backoff_factor: float = 0.5) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    带指数退避的重试装饰器。

    Args:
        retries: 最大重试次数
        backoff_factor: 退避因子

    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            last_exception = None

            for attempt in range(retries + 1):  # +1 因为第一次不算重试
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    last_exception = e

                    # 如果是最后一次尝试，不再重试
                    if attempt == retries:
                        break

                    # 分类网络错误
                    classified_error = classify_network_error(e)

                    # 某些错误不应该重试
                    if isinstance(classified_error, (AuthenticationError, AuthorizationError, ValidationError)):
                        raise classified_error

                    sleep_time = backoff_factor * (2 ** attempt)
                    print(f"请求失败: {classified_error.message}. {sleep_time:.2f}秒后重试...")
                    time.sleep(sleep_time)
                except Exception as e:
                    # 非网络异常直接抛出
                    raise MailVerifierError(f"未预期的错误: {str(e)}") from e

            # 重试耗尽，抛出分类后的异常
            if last_exception:
                classified_error = classify_network_error(last_exception)
                raise RetryExhaustedError(
                    f"重试{retries}次后仍然失败",
                    retries,
                    classified_error
                )

            # 理论上不应该到达这里
            raise RetryExhaustedError("重试机制异常终止", retries)

        return wrapper
    return decorator


class MailVerifierClient:
    """邮件验证器客户端，用于与邮件API交互获取消息和验证码。"""

    def __init__(self, token: str, base_url: str = "https://api.mail.tm"):
        """
        初始化邮件验证器客户端。

        Args:
            token: API身份验证令牌
            base_url: 邮件API的基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({"Authorization": f"Bearer {token}"})
        self.verification_parser = VerificationCodeParser()
        self.logger = get_logger("client")

        # 初始化缓存
        self.message_cache = get_message_cache()
        self.parse_cache = get_parse_cache()

        # 记录客户端初始化
        self.logger.info(
            "邮件验证器客户端初始化",
            base_url=base_url,
            token_length=len(token) if token else 0,
            cache_enabled=True
        )

    @_retry_with_backoff()
    def get_messages(self) -> List[Dict[str, Any]]:
        """
        获取所有消息列表。网络失败时自动重试。

        Returns:
            List[Dict[str, Any]]: 消息列表

        Raises:
            APIError: API请求失败
            NetworkError: 网络连接失败
            AuthenticationError: 身份验证失败
            RetryExhaustedError: 重试次数耗尽
        """
        url = f"{self.base_url}/messages"
        cache_key = f"messages:{self.base_url}"

        # 尝试从缓存获取
        cached_messages = self.message_cache.get_messages(cache_key)
        if cached_messages is not None:
            self.logger.debug("从缓存获取消息列表", url=url, count=len(cached_messages))
            return cached_messages

        start_time = time.time()

        # 记录API请求
        log_api_request("GET", url)
        self.logger.debug("开始获取消息列表", url=url)

        try:
            response = self.session.get(url)
            duration = time.time() - start_time

            # 记录API响应
            log_api_response("GET", url, response.status_code, duration)

            # 检查HTTP状态码
            if not response.ok:
                self.logger.warning(
                    "API请求失败",
                    url=url,
                    status_code=response.status_code,
                    duration=duration
                )
                response_data = None
                try:
                    response_data = response.json()
                except:
                    pass
                raise classify_http_error(response.status_code, response_data)

            # 解析响应数据
            try:
                data = response.json()
            except ValueError as e:
                self.logger.error("响应数据解析失败", url=url, error=str(e))
                raise APIError(f"响应数据格式无效: {str(e)}")

            # 验证API响应
            validator = get_validator()
            validation_result = validator.validate_api_response(data)
            if not validation_result.is_valid:
                self.logger.error("API响应验证失败", url=url, errors=validation_result.errors)
                raise APIError(f"API响应验证失败: {'; '.join(validation_result.errors)}")

            if validation_result.warnings:
                self.logger.warning("API响应验证警告", url=url, warnings=validation_result.warnings)

            messages = data.get("hydra:member", [])
            if not isinstance(messages, list):
                self.logger.error("消息列表格式错误", url=url, messages_type=type(messages).__name__)
                raise APIError("消息列表格式错误：期望列表类型")

            # 验证和清理每个消息
            cleaned_messages = []
            for i, message in enumerate(messages):
                try:
                    if isinstance(message, dict):
                        # 基本验证消息结构
                        if 'id' in message:
                            cleaned_messages.append(message)
                        else:
                            self.logger.warning(f"消息{i}缺少id字段", message_index=i)
                    else:
                        self.logger.warning(f"消息{i}格式错误", message_index=i, message_type=type(message).__name__)
                except Exception as e:
                    self.logger.warning(f"验证消息{i}时发生错误", message_index=i, error=str(e))

            self.logger.info(
                "成功获取消息列表",
                url=url,
                message_count=len(cleaned_messages),
                original_count=len(messages),
                duration=duration
            )

            # 缓存结果
            self.message_cache.set_messages(cache_key, cleaned_messages)

            return cleaned_messages

        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            self.logger.error(
                "网络请求失败",
                url=url,
                error=str(e),
                error_type=type(e).__name__,
                duration=duration
            )
            # 网络异常会被重试装饰器处理
            raise

    @_retry_with_backoff()
    def get_message_by_id(self, message_id: str) -> Dict[str, Any]:
        """
        根据ID获取单个消息。网络失败时自动重试。

        Args:
            message_id: 消息ID

        Returns:
            Dict[str, Any]: 消息数据

        Raises:
            ValidationError: 消息ID无效
            MessageNotFoundError: 消息不存在
            APIError: API请求失败
            NetworkError: 网络连接失败
            AuthenticationError: 身份验证失败
            RetryExhaustedError: 重试次数耗尽
        """
        # 验证输入参数
        if not message_id or not isinstance(message_id, str):
            self.logger.error("消息ID验证失败", message_id=message_id, message_id_type=type(message_id).__name__)
            raise ValidationError("消息ID不能为空", "message_id", message_id)

        # 尝试从缓存获取
        cached_message = self.message_cache.get_message(message_id)
        if cached_message is not None:
            self.logger.debug("从缓存获取消息详情", message_id=message_id)
            return cached_message

        url = f"{self.base_url}/messages/{message_id}"
        start_time = time.time()

        # 记录API请求
        log_api_request("GET", url, message_id=message_id)
        self.logger.debug("开始获取消息详情", url=url, message_id=message_id)

        try:
            response = self.session.get(url)
            duration = time.time() - start_time

            # 记录API响应
            log_api_response("GET", url, response.status_code, duration, message_id=message_id)

            # 特殊处理404错误
            if response.status_code == 404:
                self.logger.warning("消息不存在", message_id=message_id, url=url)
                raise MessageNotFoundError(message_id)

            # 检查其他HTTP状态码
            if not response.ok:
                self.logger.warning(
                    "获取消息失败",
                    message_id=message_id,
                    url=url,
                    status_code=response.status_code,
                    duration=duration
                )
                response_data = None
                try:
                    response_data = response.json()
                except:
                    pass
                raise classify_http_error(response.status_code, response_data)

            # 解析响应数据
            try:
                data = response.json()
            except ValueError as e:
                self.logger.error("消息数据解析失败", message_id=message_id, url=url, error=str(e))
                raise APIError(f"响应数据格式无效: {str(e)}")

            # 验证和清理消息数据
            try:
                cleaned_data = validate_and_clean_message(data)

                self.logger.info(
                    "成功获取并验证消息详情",
                    message_id=message_id,
                    url=url,
                    duration=duration,
                    has_text=bool(cleaned_data.get("text")),
                    has_html=bool(cleaned_data.get("html")),
                    text_length=len(cleaned_data.get("text", "")),
                    html_length=len(cleaned_data.get("html", ""))
                )

                # 缓存结果
                self.message_cache.set_message(message_id, cleaned_data)

                return cleaned_data

            except ValidationError as e:
                self.logger.error(
                    "消息数据验证失败",
                    message_id=message_id,
                    url=url,
                    error=e.message,
                    details=e.details
                )
                raise APIError(f"消息数据验证失败: {e.message}")

        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            self.logger.error(
                "获取消息网络请求失败",
                message_id=message_id,
                url=url,
                error=str(e),
                error_type=type(e).__name__,
                duration=duration
            )
            # 网络异常会被重试装饰器处理
            raise

    def _parse_verification_info(self, body: str, content_type: str = "text") -> Optional[str]:
        """
        解析邮件正文中的验证码或链接。
        使用增强的验证码解析器以获得更好的准确性。

        Args:
            body: 邮件正文
            content_type: 内容类型（text/html）

        Returns:
            Optional[str]: 找到的验证码/链接，未找到时返回None
        """
        if not body:
            return None

        # 尝试从解析缓存获取结果
        cached_result = self.parse_cache.get_parse_result(body)
        if cached_result is not None:
            self.logger.debug(
                "从缓存获取解析结果",
                content_length=len(body),
                content_type=content_type,
                result=cached_result
            )
            return cached_result

        # 清理邮件内容以防止安全问题
        try:
            cleaned_body = clean_email_content(body, content_type)
            self.logger.debug(
                "邮件内容清理完成",
                original_length=len(body),
                cleaned_length=len(cleaned_body),
                content_type=content_type
            )
        except Exception as e:
            self.logger.warning(
                "邮件内容清理失败，使用原始内容",
                error=str(e),
                content_type=content_type
            )
            cleaned_body = body

        # 使用清理后的内容进行解析
        result = self.verification_parser.parse(cleaned_body)

        # 缓存解析结果（包括None结果以避免重复解析）
        self.parse_cache.set_parse_result(body, result)

        return result

    def clear_cache(self) -> None:
        """清空所有缓存。"""
        self.message_cache.cache.clear()
        self.parse_cache.cache.clear()
        self.verification_parser.clear_cache()

        self.logger.info("所有缓存已清空")

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息。

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            "message_cache": self.message_cache.get_stats(),
            "parse_cache": self.parse_cache.get_stats(),
            "parser_stats": self.verification_parser.get_stats()
        }

    def cleanup_cache(self) -> None:
        """清理过期的缓存条目。"""
        self.message_cache.cleanup()
        self.parse_cache.cache.cleanup_expired()

        self.logger.debug("缓存清理完成")

    def get_latest_verification_code(
        self,
        timeout: int = 60,
        interval: int = 5,
        polling_strategy: str = "exponential_backoff"
    ) -> Optional[str]:
        """
        轮询新消息并提取最新的验证码或链接。

        Args:
            timeout: 等待消息的总时间（秒）
            interval: 初始轮询间隔（秒）
            polling_strategy: 轮询策略（fixed, exponential_backoff, adaptive, fibonacci）

        Returns:
            Optional[str]: 找到的验证码/链接，超时时返回None

        Raises:
            ValidationError: 参数无效
            VerificationCodeNotFoundError: 超时未找到验证码
            APIError: API请求失败
            NetworkError: 网络连接失败
            AuthenticationError: 身份验证失败
        """
        # 验证输入参数
        if timeout <= 0:
            self.logger.error("超时时间参数无效", timeout=timeout)
            raise ValidationError("超时时间必须大于0", "timeout", timeout)
        if interval <= 0:
            self.logger.error("轮询间隔参数无效", interval=interval)
            raise ValidationError("轮询间隔必须大于0", "interval", interval)

        # 创建智能轮询器
        try:
            strategy = PollingStrategy(polling_strategy)
        except ValueError:
            strategy = PollingStrategy.EXPONENTIAL_BACKOFF
            self.logger.warning(f"无效的轮询策略 {polling_strategy}，使用默认策略")

        poller = SmartPoller(
            strategy=strategy,
            initial_interval=interval,
            max_interval=self.config.max_polling_interval,
            backoff_factor=self.config.retry_backoff_factor
        )

        self.logger.info(
            "开始智能轮询验证码",
            timeout=timeout,
            initial_interval=interval,
            strategy=strategy.value
        )

        def check_for_verification_code() -> Optional[str]:
            """检查是否有新的验证码。"""
            try:
                messages = self.get_messages()

                if not messages:
                    self.logger.debug("未发现新消息")
                    return None

                latest_message = messages[0]
                message_id = latest_message.get("id")

                if not message_id:
                    self.logger.debug("消息缺少ID")
                    return None

                self.logger.debug(
                    "发现消息",
                    message_count=len(messages),
                    latest_message_id=message_id
                )

                try:
                    full_message = self.get_message_by_id(message_id)

                    # 提取文本内容，优先使用text字段，回退到html
                    text_body = full_message.get("text")
                    content_type = "text"

                    if not text_body and full_message.get("html"):
                        text_body = str(full_message["html"])
                        content_type = "html"

                    if text_body:
                        self.logger.debug(
                            "开始解析验证码",
                            message_id=message_id,
                            text_length=len(text_body),
                            content_type=content_type
                        )

                        code = self._parse_verification_info(text_body, content_type)
                        if code:
                            self.logger.info(
                                "成功找到验证码",
                                code=code,
                                message_id=message_id
                            )

                            # 记录审计信息
                            self.logger.audit(
                                "verification_code_found",
                                code=code,
                                message_id=message_id,
                                strategy=strategy.value
                            )

                            return code
                        else:
                            self.logger.debug(
                                "消息中未找到验证码",
                                message_id=message_id
                            )

                except MessageNotFoundError:
                    # 消息可能已被删除
                    self.logger.warning("消息已被删除", message_id=message_id)
                    return None

                return None

            except (APIError, NetworkError, AuthenticationError) as e:
                # 这些错误可能是临时的，让轮询器决定是否继续
                self.logger.warning(
                    "轮询过程中发生错误",
                    error=e.message,
                    error_type=type(e).__name__
                )
                # 抛出异常让轮询器处理
                raise e

        # 执行智能轮询
        result = poller.poll(
            check_func=check_for_verification_code,
            timeout=timeout,
            success_condition=lambda x: x is not None
        )

        # 记录轮询统计
        stats = poller.get_stats()
        self.logger.info(
            "轮询完成",
            success=result.success,
            attempts=result.attempts,
            total_time=result.total_time,
            strategy=result.strategy_used.value if result.strategy_used else "unknown",
            stats=stats
        )

        if result.success:
            return result.result
        else:
            # 轮询失败
            if result.error:
                if isinstance(result.error, TimeoutError):
                    raise VerificationCodeNotFoundError(
                        f"在{timeout}秒内未找到验证码（共尝试{result.attempts}次）",
                        timeout
                    )
                else:
                    raise result.error
            else:
                raise VerificationCodeNotFoundError(
                    f"轮询失败（共尝试{result.attempts}次）",
                    timeout
                )
