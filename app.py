import sys

from mail_verifier import MailVerifierClient

# --- 使用说明 ---
# 1. 安装依赖:
#    pip install .
#
# 2. 设置环境变量:
#    在您的终端中设置 MAIL_TM_TOKEN 环境变量。
#    - Windows (CMD): set MAIL_TM_TOKEN="your-token-here"
#    - Windows (PowerShell): $env:MAIL_TM_TOKEN="your-token-here"
#    - Linux/macOS: export MAIL_TM_TOKEN="your-token-here"
#
# 3. 运行脚本:
#    python app.py
# ----------------

# 从环境变量或安全的地方获取你的API令牌
# 注意：不要将令牌硬编码在代码中
API_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************.v6Jq9hp180PYOLW6ydcSoKUbXhvfzGJkuULeh8H3Dhf1sRmfznCctIJ9Q7D5yKcbBafIV_olb4VpbyUztQp_1Q"

def main():
    """
    主函数，演示如何使用 MailVerifierClient。
    """
    if not API_TOKEN:
        print("错误：MAIL_TM_TOKEN 环境变量未设置。", file=sys.stderr)
        print("请参考脚本顶部的说明来设置您的 API 令牌。", file=sys.stderr)
        sys.exit(1)

    # 初始化客户端
    client = MailVerifierClient(token=API_TOKEN)
    print("客户端初始化成功。")
    print("正在等待新的邮件验证码（最长等待10秒）...")

    # 获取最新的验证码，最长等待10秒
    try:
        verification_code = client.get_latest_verification_code(timeout=10)

        if verification_code:
            print(f"\n🎉 成功获取验证码: {verification_code}")
        else:
            print("\n😔 在超时时间内未找到包含验证码的新邮件。")

    except Exception as e:
        print(f"\n发生错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
